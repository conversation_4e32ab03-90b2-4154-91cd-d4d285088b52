"""API代理服务核心逻辑

提供以下主要功能:
- 多提供商负载均衡(Round-robin)
- 自动故障转移和重试
- 请求监控和统计
- 健康检查和状态管理
- 作业失败智能分析

使用示例:
    config = Config({
        "openai": [
            ProviderConfig("account1", "sk-xxx1"),
            ProviderConfig("account2", "sk-xxx2")
        ]
    })
    service = ProxyService(config)
    response = service.call("openai", "chat/completions",
                         messages=[{"role":"user","content":"hello"}])
"""

import logging
import time
import threading
from typing import Dict, List, Optional, Any
from .providers.base import BaseProvider
from .config import Config
from .utils import (
    get_round_robin_provider,
    record_provider_success,
    record_provider_failure,
    get_provider_health_info
)
from .models import ProviderConfig
from .monitoring import RequestMonitor
from .health_check import HealthChecker

from enum import Enum, auto
from dataclasses import dataclass
from datetime import datetime
import re

from .job_analysis import JobErrorType, JobFailureAnalysis, JobAnalyzer


class ProxyService:
    """统一API代理服务,支持多提供商轮换

    主要功能:
    - 多供应商负载均衡
    - 自动故障转移
    - 请求监控
    - 健康检查
    - 作业失败分析
    """

    def __init__(self, config: Config):
        """初始化代理服务

        Args:
            config (Config): 服务配置

        Raises:
            ValueError: 如果配置无效
        """
        if not isinstance(config, Config):
            raise ValueError("无效的配置类型")

        self.config = config
        self.providers: Dict[str, List[BaseProvider]] = {}
        self.monitor = RequestMonitor()
        self.health_checker = HealthChecker(
            interval=60, timeout=10, max_retries=3  # 默认60秒检查一次  # 10秒超时
        )
        self.logger = logging.getLogger(__name__)
        self._lock = threading.RLock()  # 用于线程安全的热重载
        self._init_providers()

    def _init_providers(self):
        """从配置初始化提供商"""
        with self._lock:
            for provider_type, provider_configs in self.config.providers.items():
                self.providers[provider_type] = [
                    self._create_provider(provider_type, config)
                    for config in provider_configs
                ]

    def register_provider(self, provider_type: str, provider: BaseProvider):
        """注册新的API提供商"""
        if provider_type not in self.providers:
            self.providers[provider_type] = []
        self.providers[provider_type].append(provider)

    def analyze_job_failure(self, log_text: str) -> List[JobFailureAnalysis]:
        """分析作业失败日志

        使用JobAnalyzer进行日志分析并返回结果

        Args:
            log_text: 作业日志文本

        Returns:
            包含所有错误分析结果列表
        """
        analyzer = JobAnalyzer()
        return analyzer.analyze_log(log_text)

    def _create_provider(self, provider_type: str, config: ProviderConfig) -> BaseProvider:
        """根据配置创建提供商实例

        Args:
            provider_type: 提供商类型 (如 'openai')
            config: 提供商配置

        Returns:
            BaseProvider: 创建的提供商实例

        Raises:
            ValueError: 不支持的提供商类型
        """
        if provider_type == "openai":
            from .providers.openai import OpenAIProvider
            return OpenAIProvider(config.api_key)
        elif provider_type == "openrouter":
            from .providers.openrouter import OpenRouterProvider
            # 从配置中获取可选参数
            timeout = config.config.get("timeout", 30)
            site_url = config.config.get("site_url", "")
            site_name = config.config.get("site_name", "")
            return OpenRouterProvider(config.api_key, timeout=timeout, site_url=site_url, site_name=site_name)
        else:
            raise ValueError(f"不支持的提供商类型: {provider_type}")

    def get_provider(self, provider_type: str) -> BaseProvider:
        """获取当前轮询到的API提供商

        Args:
            provider_type: 提供商类型

        Returns:
            BaseProvider: 轮询到的提供商实例

        Raises:
            ValueError: 提供商类型不存在或无可用提供商
        """
        if provider_type not in self.providers:
            raise ValueError(f"提供商类型 '{provider_type}' 不存在")

        providers = self.providers[provider_type]
        if not providers:
            raise ValueError(f"提供商类型 '{provider_type}' 无可用实例")

        return get_round_robin_provider(providers)

    def _get_provider_instance_name(self, provider_type: str, provider_instance: BaseProvider) -> str:
        """获取提供商实例的唯一名称

        Args:
            provider_type: 提供商类型
            provider_instance: 提供商实例

        Returns:
            str: 实例的唯一名称，格式为 "provider_type:instance_name"
        """
        # 在提供商列表中找到这个实例的索引和配置名称
        providers = self.providers.get(provider_type, [])
        provider_configs = self.config.providers.get(provider_type, [])

        for i, provider in enumerate(providers):
            if provider is provider_instance:
                if i < len(provider_configs):
                    instance_name = provider_configs[i].name
                    return f"{provider_type}:{instance_name}"
                else:
                    return f"{provider_type}:instance_{i}"

        # 如果找不到，使用对象ID作为后备
        return f"{provider_type}:unknown_{id(provider_instance)}"

    def call(self, provider_type: str, endpoint: str, **kwargs) -> Dict[str, Any]:
        """调用指定提供商的API，支持智能故障转移

        Args:
            provider_type: 提供商类型 (如 'openai')
            endpoint: API端点路径 (如 'chat/completions')
            **kwargs: API请求参数

        Returns:
            Dict[str, Any]: API响应数据

        Raises:
            ValueError: 参数无效或提供商不存在
            Exception: 所有提供商都失败时抛出最后一个错误
        """
        if not provider_type or not isinstance(provider_type, str):
            raise ValueError("提供商类型必须是非空字符串")
        if not endpoint or not isinstance(endpoint, str):
            raise ValueError("端点路径必须是非空字符串")

        # 检查提供商类型是否存在
        if provider_type not in self.providers:
            raise ValueError(f"提供商类型 '{provider_type}' 不存在")

        providers = self.providers[provider_type]
        if not providers:
            raise ValueError(f"提供商类型 '{provider_type}' 无可用实例")

        # 提取模型信息（如果存在）
        model = kwargs.get('model', None)

        # 记录请求开始时间
        start_time = time.time()
        last_error = None

        self.logger.info(f"🎯 开始智能多账户负载均衡调用: {provider_type}/{endpoint}")
        self.logger.info(f"   可用提供商数量: {len(providers)}")
        if model:
            self.logger.info(f"   模型: {model}")

        # 显示提供商健康状态
        health_info = get_provider_health_info(providers)
        self.logger.debug(f"   提供商健康状态: {health_info}")

        # 尝试所有可用的提供商实例
        tried_providers = set()  # 记录已尝试的提供商，避免重复

        for attempt in range(len(providers)):
            # 使用智能轮询选择提供商
            provider = get_round_robin_provider(providers)
            provider_key = id(provider)

            # 如果已经尝试过这个提供商，跳过
            if provider_key in tried_providers:
                continue
            tried_providers.add(provider_key)

            provider_instance_name = self._get_provider_instance_name(provider_type, provider)

            success = False
            error_type = None

            try:
                self.logger.info(f"🔄 智能选择提供商 {attempt + 1}/{len(providers)}: {provider_instance_name}")

                # 调用提供商API
                response = provider.call(endpoint, **kwargs)
                success = True

                self.logger.info(f"✅ 提供商调用成功: {provider_instance_name}")
                self.logger.debug(f"   响应类型: {type(response)}")
                if isinstance(response, dict):
                    self.logger.debug(f"   响应键: {list(response.keys())}")

                # 记录成功状态
                record_provider_success(provider)

                # 记录成功请求统计
                duration = time.time() - start_time
                self.monitor.record_request(provider_instance_name, duration, success, error_type, model, endpoint)

                return response

            except Exception as e:
                last_error = e
                error_type = type(e).__name__
                error_msg = str(e)

                # 记录失败状态
                record_provider_failure(provider, error_msg)

                # 判断是否应该尝试下一个提供商
                should_retry = self._should_retry_with_next_provider(e)

                self.logger.warning(f"❌ 提供商调用失败: {provider_instance_name}")
                self.logger.warning(f"   错误类型: {error_type}")
                self.logger.warning(f"   错误信息: {error_msg}")
                self.logger.info(f"   是否尝试下一个提供商: {should_retry}")

                # 记录失败请求统计
                duration = time.time() - start_time
                self.monitor.record_request(provider_instance_name, duration, success, error_type, model, endpoint)

                # 如果不应该重试或者已经尝试了所有提供商，则停止尝试
                if not should_retry or len(tried_providers) >= len(providers):
                    break

                self.logger.error(f"🔄 智能切换到下一个健康提供商...")

        # 所有提供商都失败了
        self.logger.error(f"❌ 所有 {len(providers)} 个提供商都失败了")
        self.logger.error(f"   最后错误: {last_error}")

        # 添加完整的堆栈跟踪
        import traceback
        self.logger.error(f"   完整堆栈跟踪:")
        for line in traceback.format_exc().split('\n'):
            if line.strip():
                self.logger.error(f"     {line}")

        raise last_error if last_error else Exception("所有提供商都不可用")

    def _should_retry_with_next_provider(self, error: Exception) -> bool:
        """判断是否应该尝试下一个提供商

        Args:
            error: 发生的错误

        Returns:
            bool: 是否应该尝试下一个提供商
        """
        error_msg = str(error).lower()
        error_type = type(error).__name__

        # 速率限制错误 - 应该尝试下一个提供商
        if "rate limit" in error_msg or "429" in error_msg:
            self.logger.info(f"🚫 检测到速率限制错误，尝试下一个提供商")
            return True

        # 配额超限错误 - 应该尝试下一个提供商
        if "quota" in error_msg or "exceeded" in error_msg:
            self.logger.info(f"🚫 检测到配额超限错误，尝试下一个提供商")
            return True

        # 认证错误 - 应该尝试下一个提供商（可能是密钥失效）
        if "unauthorized" in error_msg or "401" in[object Object]，尝试下一个提供商")
            return True

        # 服务不可用错误 - 应该尝试下一个提供商
        if "503" in error_msg or "502" in error_msg or "500" in error_msg:
            self.logger.info(f"🚫 检测到服务错误，尝试下一个提供商")
            return True

        # 超时错误 - 应该尝试下一个提供商
        if "timeout" in error_msg or error_type == "TimeoutError":
            self.logger.info(f"🚫 检测到超时错误，尝试下一个提供商")
            return True

        # 连接错误 - 应该尝试下一个提供商
        if "connection" in[object Object] 检测到连接错误，尝试下一个提供商")
            return True

        # 模型不可用错误 - 应该尝试下一个提供商
        if "model" in error_msg and ("not found" in[object Object]用错误，尝试下一个提供商")
            return True

        # 其他错误 - 不重试（可能是请求参数问题）
        self.logger.warning(f"🔍 非重试类型错误，停止尝试其他提供商")
        return False

    def hot_reload(self, new_config: Config):
        """热重载服务配置

        Args:
            new_config: 新的配置对象

        Raises:
            ValueError: 如果新配置无效
        """
        if not isinstance(new_config, Config):
            raise ValueError("无效的配置类型")

        with self._lock:
            try:
                self.logger.info("开始热重载服务配置...")

                # 保存旧配置和提供商
                old_config = self.config
                old_providers = self.providers.copy()

                # 更新配置
                self.config = new_config

                # 重新初始化提供商
                self.providers.clear()
                self._init_providers()

                # 更新健康检查器配置
                self.health_checker.interval = new_config.health_check_interval

                # 更新日志级别
                if old_config.log_level != new_config.log_level:
                    logging.getLogger().setLevel(getattr(logging, new_config.log_level.upper()))

                self.logger.info("服务配置热重载成功")

                # 清理旧提供商资源（如果需要）
                self._cleanup_old_providers(old_providers)

            except Exception as e:
                self.logger.error(f"热重载失败，回滚到旧配置: {e}")
                # 回滚配置
                self.config = old_config
                self.providers = old_providers
                raise

    def _cleanup_old_providers(self, old_providers: Dict[str, List[BaseProvider]]):
        """清理旧的提供商资源"""
        try:
            for provider_type, providers in old_providers.items():
                for provider in providers:
                    if hasattr(provider, 'cleanup'):
                        try:
                            provider.cleanup()
                        except Exception as e:
                            self.logger.warning(f"清理提供商 {provider_type} 资源失败: {e}")
        except Exception as e:
            self.logger.warning(f"清理旧提供商资源时出错: {e}")

    def get_provider_safely(self, provider_type: str) -> Optional[BaseProvider]:
        """线程安全地获取提供商"""
        with self._lock:
            try:
                return self.get_provider(provider_type)
            except ValueError:
                return None

    def get_service_status(self) -> Dict[str, Any]:
        """获取服务状态信息"""
        with self._lock:
            provider_status = {}
            for provider_type, providers in self.providers.items():
                provider_status[provider_type] = {
                    "count": len(providers),
                    "instances": [
                        {
                            "name": getattr(provider, 'name', 'unknown'),
                            "last_used": getattr(provider, 'last_used', None)
                        }
                        for provider in providers
                    ]
                }

            return {
                "config": {
                    "provider_types": list(self.config.providers.keys()),
                    "total_providers": sum(len(providers) for providers in self.providers.values()),
                    "default_timeout": self.config.default_timeout,
                    "max_retries": self.config.max_retries,
                    "monitoring_enabled": self.config.enable_monitoring,
                    "log_level": self.config.log_level
                },
                "providers": provider_status,
                "monitoring": self.monitor.get_stats() if self.config.enable_monitoring else None,
                "health_check_interval": self.health_checker.interval
            }

    def cleanup(self):
        """清理服务资源"""
        with self._lock:
            self.logger.info("正在清理服务资源...")

            # 保存监控统计数据
            if hasattr(self.monitor, 'cleanup'):
                self.monitor.cleanup()

            self._cleanup_old_providers(self.providers)
            self.providers.clear()
            self.logger.info("服务资源清理完成")
